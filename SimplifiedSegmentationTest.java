public class SimplifiedSegmentationTest {
    
    public static void main(String[] args) {
        // Test different durations
        double[] testDurations = {0.5, 1.0, 2.5, 3.0, 4.0, 4.9, 5.0, 6.0, 7.0, 8.0, 9.0, 10.0, 11.0, 12.0, 15.0, 20.0, 25.0};
        
        System.out.println("Testing simplified audio segmentation algorithm");
        System.out.println("Rule: < 5s -> return 5, >= 5s -> return 3/4/5 with last segment >= 1s");
        System.out.println("=".repeat(90));
        
        for (double duration : testDurations) {
            int segmentDuration = calculateAudioSegmentation(duration);
            
            // Verify the result
            String verification = verifySegmentation(duration, segmentDuration);
            boolean isAllowed = (segmentDuration == 3 || segmentDuration == 4 || segmentDuration == 5);
            boolean followsRule = (duration < 5.0 && segmentDuration == 5) || (duration >= 5.0);
            
            System.out.printf("Duration: %5.1fs -> Segment: %ds [%s] %s %s%n", 
                    duration, segmentDuration, verification, 
                    isAllowed ? "✓VALID" : "✗INVALID",
                    followsRule ? "✓RULE" : "✗RULE");
        }
    }
    
    private static int calculateAudioSegmentation(double totalDurationSeconds) {
        // Rule 1: < 5s -> return 5
        if (totalDurationSeconds < 5.0) {
            return 5;
        }

        // Rule 2: >= 5s -> find best from 3,4,5 with last segment >= 1s
        int[] allowedDurations = {5, 4, 3};

        for (int segmentDuration : allowedDurations) {
            int fullSegments = (int) Math.floor(totalDurationSeconds / segmentDuration);
            double remainder = totalDurationSeconds - (fullSegments * segmentDuration);

            // If divisible (remainder ~= 0) or remainder >= 1s, this is suitable
            if (remainder < 0.001 || remainder >= 1.0) {
                return segmentDuration;
            }
        }

        // If 3, 4, 5 don't work, choose the one with largest remainder
        int bestSegmentDuration = 5;
        double bestLastSegment = 0.0;

        for (int segmentDuration : allowedDurations) {
            int fullSegments = (int) Math.floor(totalDurationSeconds / segmentDuration);
            double remainder = totalDurationSeconds - (fullSegments * segmentDuration);

            if (remainder > bestLastSegment) {
                bestLastSegment = remainder;
                bestSegmentDuration = segmentDuration;
            }
        }

        return bestSegmentDuration;
    }
    
    private static String verifySegmentation(double totalDuration, int segmentDuration) {
        if (totalDuration < 5.0) {
            return "OK (< 5s, no split)";
        }
        
        int fullSegments = (int) Math.floor(totalDuration / segmentDuration);
        double lastSegmentDuration = totalDuration - (fullSegments * segmentDuration);
        
        if (Math.abs(lastSegmentDuration) < 0.001) {
            return String.format("OK (Perfect: %d×%ds)", fullSegments, segmentDuration);
        } else if (lastSegmentDuration >= 1.0) {
            return String.format("OK (%d×%ds + %.2fs)", fullSegments, segmentDuration, lastSegmentDuration);
        } else {
            return String.format("FAIL (Last: %.2fs < 1.0s)", lastSegmentDuration);
        }
    }
}
